//
//  ChessAnnotationManager.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit
import Combine

/// Manages chess annotations including visual annotations, text comments, and assessments
@MainActor
final class ChessAnnotationManager: ObservableObject {
    
    // MARK: - Callbacks
    /// Called when annotations are modified and cache needs to be invalidated
    var onAnnotationChanged: (() -> Void)?
    
    // MARK: - Initialization
    init() {}
    
    // MARK: - Visual Annotations
    
    /// Toggles a square highlight for the current move position
    /// - Parameters:
    ///   - square: The square to toggle highlight on
    ///   - session: The game session containing the current move
    func toggleSquareHighlight(at square: Square, in session: GameSession) {
        print("Attempting to toggle square highlight at \(square) with color \(session.currentAnnotationColor)")
        
        guard var move = session.game.moves.getNodeMove(index: session.currentMoveIndex) else {
            // If there's no move at the current index, we can't add an annotation.
            // This can happen at the start of the game. We might want to handle this
            // by creating a "null" move or associating annotations with positions.
            // For now, we just print a message.
            print("Cannot toggle highlight: No move at current index.")
            return
        }

        var squareHighlights = move.positionComment.visualAnnotations.squareHighlights
        let color = session.currentAnnotationColor
        
        // Check if there's already a highlight on this square
        if let existingIndex = squareHighlights.firstIndex(where: { $0.square == square }) {
            let existingHighlight = squareHighlights[existingIndex]
            if existingHighlight.color == color {
                // Same color exists - remove it
                squareHighlights.remove(at: existingIndex)
                print("Removed highlight at \(square)")
            } else {
                // Different color exists - change to the new color
                squareHighlights[existingIndex] = Move.VisualAnnotations.SquareHighlight(color: color, square: square)
                print("Changed highlight color at \(square) to \(color)")
            }
        } else {
            // No highlight exists - add new one
            squareHighlights.append(Move.VisualAnnotations.SquareHighlight(color: color, square: square))
            print("Added new highlight at \(square) with color \(color)")
        }
        
        // Update the move with modified visual annotations
        move.positionComment.visualAnnotations.squareHighlights = squareHighlights
        session.isModified = true
        _ = session.editMove(at: session.currentMoveIndex, newMove: move)

        // Notify that annotations have changed
        onAnnotationChanged?()
    }
    
    /// Toggles an arrow annotation for the current move position
    /// - Parameters:
    ///   - from: The starting square of the arrow
    ///   - to: The ending square of the arrow
    ///   - session: The game session containing the current move
    func toggleArrow(from: Square, to: Square, in session: GameSession) {
        print("Attempting to toggle arrow from \(from) to \(to) with color \(session.currentAnnotationColor)")
        
        guard var move = session.game.moves.getNodeMove(index: session.currentMoveIndex) else {
            print("Cannot toggle arrow: No move at current index.")
            return
        }

        var arrows = move.positionComment.visualAnnotations.arrows
        let color = session.currentAnnotationColor
        
        // Check if there's already an arrow between these squares
        if let existingIndex = arrows.firstIndex(where: { $0.from == from && $0.to == to }) {
            let existingArrow = arrows[existingIndex]
            if existingArrow.color == color {
                // Same color exists - remove it
                arrows.remove(at: existingIndex)
                print("Removed arrow from \(from) to \(to)")
            } else {
                // Different color exists - change to the new color
                arrows[existingIndex] = Move.VisualAnnotations.Arrow(color: color, from: from, to: to)
                print("Changed arrow color from \(from) to \(to) to \(color)")
            }
        } else {
            // No arrow exists - add new one
            arrows.append(Move.VisualAnnotations.Arrow(color: color, from: from, to: to))
            print("Added new arrow from \(from) to \(to) with color \(color)")
        }
        
        // Update the move with modified visual annotations
        move.positionComment.visualAnnotations.arrows = arrows
        session.isModified = true
        _ = session.editMove(at: session.currentMoveIndex, newMove: move)

        // Notify that annotations have changed
        onAnnotationChanged?()
    }
    
    // MARK: - Text Comments
    
    /// Sets the comment text for a move at the given index
    /// - Parameters:
    ///   - text: The comment text to set
    ///   - moveIndex: The index of the move to comment on
    ///   - session: The game session containing the move
    func setMoveCommentText(_ text: String, at moveIndex: MoveTree.MoveIndex, in session: GameSession) {
        guard var move = session.game.moves.getNodeMove(index: moveIndex) else {
            print("Cannot set comment: No move at index \(moveIndex)")
            return
        }
        
        move.positionComment.text = text
        session.isModified = true
        _ = session.editMove(at: moveIndex, newMove: move)
        
        // Notify that annotations have changed
        onAnnotationChanged?()
    }
    
    /// Gets the current comment text at the given index
    /// - Parameters:
    ///   - moveIndex: The index of the move to get comment from
    ///   - session: The game session containing the move
    /// - Returns: The comment text, or empty string if no comment exists
    func getMoveCommentText(at moveIndex: MoveTree.MoveIndex, in session: GameSession) -> String {
        return session.game.moves.getNodeMove(index: moveIndex)?.positionComment.text ?? ""
    }
    
    // MARK: - Move and Position Assessments
    
    /// Sets the move assessment for a move at the given index
    /// - Parameters:
    ///   - assessment: The assessment to set (!, ?, !!, etc.)
    ///   - moveIndex: The index of the move to assess
    ///   - session: The game session containing the move
    func setMoveAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex, in session: GameSession) {
        guard var move = session.game.moves.getNodeMove(index: moveIndex),
              var metaMove = move.metaMove else {
            print("Cannot set move assessment: No move or metaMove at index \(moveIndex)")
            return
        }
        
        metaMove.moveAssessment = assessment
        move.metaMove = metaMove
        session.isModified = true
        _ = session.editMove(at: moveIndex, newMove: move)
        
        // Notify that annotations have changed
        onAnnotationChanged?()
    }
    
    /// Sets the position assessment for a move at the given index
    /// - Parameters:
    ///   - assessment: The assessment to set (+/-, =, ∞, etc.)
    ///   - moveIndex: The index of the move to assess
    ///   - session: The game session containing the move
    func setPositionAssessment(_ assessment: MetaMove.Assessment, at moveIndex: MoveTree.MoveIndex, in session: GameSession) {
        guard var move = session.game.moves.getNodeMove(index: moveIndex),
              var metaMove = move.metaMove else {
            print("Cannot set position assessment: No move or metaMove at index \(moveIndex)")
            return
        }
        
        metaMove.positionAssessment = assessment
        move.metaMove = metaMove
        session.isModified = true
        _ = session.editMove(at: moveIndex, newMove: move)
        
        // Notify that annotations have changed
        onAnnotationChanged?()
    }
    
    // MARK: - Annotation Color Management
    
    /// Sets the current annotation color for the session
    /// - Parameters:
    ///   - color: The color to set for new annotations
    ///   - session: The game session to update
    func setAnnotationColor(_ color: Move.VisualAnnotations.AnnotationColor, in session: GameSession) {
        session.currentAnnotationColor = color
        print("Annotation color set to: \(color)")
    }
    
    // MARK: - Board Orientation
    
    /// Toggles the board orientation
    /// - Parameter session: The game session to update
    func toggleBoardFlip(in session: GameSession) {
        session.isBoardFlipped.toggle()
        print("Board flipped: \(session.isBoardFlipped)")
    }
}
