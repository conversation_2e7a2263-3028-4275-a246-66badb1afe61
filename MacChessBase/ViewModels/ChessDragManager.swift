//
//  ChessDragManager.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit
import Combine

/// Manages chess drag and drop interactions including normal and reverse drag operations
@MainActor
final class ChessDragManager: ObservableObject {
    
    // MARK: - Normal Drag State
    @Published var selectedSquare: Square?
    @Published var possibleMoves: [Square] = []
    
    // MARK: - Reverse Drag State (target-first selection via drag)
    @Published var isReverseDragActive: Bool = false
    @Published var reverseDragTarget: Square?
    @Published var reverseDragValidSources: [Square] = []
    
    // MARK: - Initialization
    init() {}
    
    // MARK: - Normal Drag Operations
    
    /// Validates if a drag operation can start for the given piece and square
    /// - Parameters:
    ///   - piece: The piece being dragged
    ///   - square: The square the piece is being dragged from
    ///   - session: The game session containing the current board state
    /// - Returns: True if the drag can start, false otherwise
    func validateDragStart(piece: Piece, from square: Square, in session: GameSession) -> <PERSON><PERSON> {
        guard piece.color == session.board.position.sideToMove else { return false }
        return true
    }
    
    /// Sets the selected square for move validation and calculates possible moves
    /// - Parameters:
    ///   - square: The square to select
    ///   - session: The game session containing the current board state
    func setSelectedSquare(_ square: Square, in session: GameSession) {
        selectedSquare = square
        possibleMoves = session.board.legalMoves(forPieceAt: square)
    }
    
    /// Selects a square if it contains a piece of the current player
    /// - Parameters:
    ///   - square: The square to attempt to select
    ///   - session: The game session containing the current board state
    func selectSquare(_ square: Square, in session: GameSession) {
        guard let piece = session.board.position.piece(at: square),
              piece.color == session.board.position.sideToMove else {
            clearSelection()
            return
        }
        
        selectedSquare = square
        possibleMoves = session.board.legalMoves(forPieceAt: square)
    }
    
    /// Checks if a piece at the given square can be moved (has legal moves)
    /// - Parameters:
    ///   - square: The square to check
    ///   - session: The game session containing the current board state
    /// - Returns: True if the piece can be moved, false otherwise
    func canMovePiece(at square: Square, in session: GameSession) -> Bool {
        guard let piece = session.board.position.piece(at: square),
              piece.color == session.board.position.sideToMove else { return false }
        return !session.board.legalMoves(forPieceAt: square).isEmpty
    }
    
    /// Clears the current selection
    func clearSelection() {
        selectedSquare = nil
        possibleMoves = []
    }
    
    /// Clears all selections (normal and reverse drag)
    func clearAllSelections() {
        clearSelection()
        cancelReverseDrag()
    }
    
    // MARK: - Reverse Drag Operations
    
    /// Finds all source squares that can move to the given target square
    /// - Parameters:
    ///   - targetSquare: The target square to find sources for
    ///   - session: The game session containing the current board state
    /// - Returns: Array of squares that contain pieces that can move to the target
    func findSourceSquares(for targetSquare: Square, in session: GameSession) -> [Square] {
        var sourceSquares: [Square] = []

        // Check all squares on the board
        for rank in 1...8 {
            for file in ["a", "b", "c", "d", "e", "f", "g", "h"] {
                let square = Square("\(file)\(rank)")
                let piece = session.board.position.piece(at: square)

                // Only check pieces of the current player
                guard let piece = piece, piece.color == session.board.position.sideToMove else { continue }

                // Check if this piece can move to the target square
                let legalMoves = session.board.legalMoves(forPieceAt: square)
                if legalMoves.contains(targetSquare) {
                    sourceSquares.append(square)
                }
            }
        }

        print("findSourceSquares for \(targetSquare): found \(sourceSquares.count) pieces - \(sourceSquares)")
        return sourceSquares
    }
    
    /// Starts a reverse drag from a target square
    /// - Parameters:
    ///   - targetSquare: The square to start reverse drag from
    ///   - session: The game session containing the current board state
    /// - Returns: True if reverse drag started successfully, false otherwise
    func startReverseDrag(from targetSquare: Square, in session: GameSession) -> Bool {
        let piece = session.board.position.piece(at: targetSquare)
        
        print("startReverseDrag from \(targetSquare), piece: \(piece?.description ?? "empty"), sideToMove: \(session.board.position.sideToMove)")
        
        // Only allow reverse drag from empty squares or opponent pieces
        if piece == nil || piece?.color != session.board.position.sideToMove {
            let validSources = findSourceSquares(for: targetSquare, in: session)
            
            // Enter reverse drag mode
            isReverseDragActive = true
            reverseDragTarget = targetSquare
            reverseDragValidSources = validSources
            clearSelection() // Clear normal selection
            
            print("startReverseDrag success, found \(validSources.count) source pieces: \(validSources)")
            return true
        }
        print("startReverseDrag failed - trying to drag own piece")
        return false
    }
    
    /// Completes a reverse drag to a source square
    /// - Parameters:
    ///   - sourceSquare: The source square to complete the reverse drag to
    ///   - session: The game session containing the current board state
    ///   - onMoveAttempt: Callback to execute the actual move
    func completeReverseDrag(to sourceSquare: Square, in session: GameSession, onMoveAttempt: (Square, Square) -> Void) {
        guard isReverseDragActive,
              let targetSquare = reverseDragTarget else {
            cancelReverseDrag()
            return
        }
        
        print("completeReverseDrag from \(sourceSquare) to \(targetSquare)")
        
        // Check if the source square is valid for this reverse drag
        if reverseDragValidSources.contains(sourceSquare) {
            print("Valid reverse drag move, attempting move from \(sourceSquare) to \(targetSquare)")
            onMoveAttempt(sourceSquare, targetSquare)
        } else {
            print("Invalid reverse drag move - source \(sourceSquare) not in valid sources: \(reverseDragValidSources)")
        }
        
        cancelReverseDrag()
    }
    
    /// Cancels the reverse drag operation
    func cancelReverseDrag() {
        isReverseDragActive = false
        reverseDragTarget = nil
        reverseDragValidSources = []
    }
    
    // MARK: - Computed Properties for UI
    
    /// Returns true if the given square is the current reverse drag target
    /// - Parameter square: The square to check
    /// - Returns: True if this square is the reverse drag target
    func isReverseDragTarget(_ square: Square) -> Bool {
        return isReverseDragActive && reverseDragTarget == square
    }
    
    /// Returns true if the given square is a valid source for the current reverse drag
    /// - Parameter square: The square to check
    /// - Returns: True if this square is a valid reverse drag source
    func isReverseDragSource(_ square: Square) -> Bool {
        return isReverseDragActive && reverseDragValidSources.contains(square)
    }
}
